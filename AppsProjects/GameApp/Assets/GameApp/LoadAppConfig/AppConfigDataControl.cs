﻿using UnityEngine;
using System.Collections;

public class AppConfigDataControl : MonoBehaviour {

	public GameObject appConfigItemPrefab;
	
	public static bool IsHaveappConfigItem = false;  
	private GameObject clone;
	// Use this for initialization
	void Start () {
		try
		{
			Debug.Log("AppConfigDataControl: Starting initialization...");

			//Caching.ClearCache();

			if (!IsHaveappConfigItem)
			{
				// 检查预制体是否已赋值
				if (appConfigItemPrefab == null)
				{
					Debug.LogError("AppConfigDataControl: appConfigItemPrefab is null! Please assign it in the Inspector.");
					return;
				}

				clone = Instantiate(appConfigItemPrefab, transform.position, transform.rotation);
				if (clone != null)
				{
					clone.transform.localPosition = new Vector3(0, 0, 0);
					clone.name = "AppConfigDataItem";
					IsHaveappConfigItem = true;

					// 尝试获取LoadAppConfig组件并初始化
					if (clone.TryGetComponent(out LoadAppConfig loadAppConfig))
					{
						Debug.Log("AppConfigDataControl: Starting WebDataInit...");
						loadAppConfig.WebDataInit();
					}
					else
					{
						Debug.LogWarning("AppConfigDataControl: LoadAppConfig component not found on prefab! Adding component...");
						// 如果组件不存在，动态添加
						loadAppConfig = clone.AddComponent<LoadAppConfig>();
						if (loadAppConfig != null)
						{
							Debug.Log("AppConfigDataControl: LoadAppConfig component added successfully, starting WebDataInit...");
							loadAppConfig.WebDataInit();
						}
					}

					// 确保对象在场景切换时不被销毁
					DontDestroyOnLoad(clone);
					Debug.Log("AppConfigDataControl: AppConfigDataItem created and initialized successfully.");
				}
				else
				{
					Debug.LogError("AppConfigDataControl: Failed to instantiate appConfigItemPrefab!");
				}
			}
			else
			{
				Debug.Log("AppConfigDataControl: AppConfigDataItem already exists, skipping creation.");
			}
		}
		catch (System.Exception ex)
		{
			Debug.LogError("AppConfigDataControl Start Exception: " + ex.Message);
			Debug.LogError("Stack Trace: " + ex.StackTrace);
		}
	}
	
	/*
	// Update is called once per frame
	void Update () {
	
	}
	*/
}
