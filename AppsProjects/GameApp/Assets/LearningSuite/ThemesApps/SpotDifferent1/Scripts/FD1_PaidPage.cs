using FairyGUI;
using FairyGUI.Utils;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Text.RegularExpressions;
using SA.IOSNative.StoreKit;
using SA.Common.Models;
using System.Threading.Tasks;
using UnityEngine.UI;
using System.Net.Http;
using System.Text;
using UnityEngine.Networking;
using Newtonsoft.Json;
using Unity.Services.Core;
using Unity.Services.Core.Environments;
using System.Linq;
using UIPanel = FairyGUI.UIPanel;
using GComponent = FairyGUI.GComponent;
using GRoot = FairyGUI.GRoot;
using UIContentScaler = FairyGUI.UIContentScaler;
using UIPackage = FairyGUI.UIPackage;
using EventCallback0 = FairyGUI.EventCallback0;
using RelationType = FairyGUI.RelationType;
using LitJson;
using FD1_UI.UserCenter;


// 数据类已移动到 PaidContentDataClasses.cs 文件中



public class UserPaidPageControl : MonoBehaviour
{
    public string packageName = "UserCenter";
    private bool isTransitioning = false;
    public GameObject _UIPanelGameObject;
    private UIPanel _uiPanel;


    private AudioClip btnClickSound;


    private bool isValidEmail, isValidPassword;
    private EventCallback0 emailCheckCallback;
    private EventCallback0 passwordCheckCallback;


    private GComponent _viewMain;


    private Dictionary<string, IAPProduct> productConfigs = new Dictionary<string, IAPProduct>();

    private enum PaidSubscirbPlan
    {
        year,
        monthly
    }

    private PaidSubscirbPlan paidSubscirbPlan = PaidSubscirbPlan.year;

    private bool canCheckUnLock = false;
    private string _errorMessage;
    private int actionId = 1; //1: buy, 2:restore
    private bool isPriceDataLoaded = false; // 标志位：价格数据是否已从App Store加载
    enum ActionType
    {
        buy,
        restore,
        shop,
    }
    ActionType action = ActionType.shop;

    private bool isProcessingTransaction = false;

    public string backSceneName = "FD1_HomePage";
    public string configName = "Configs/Data_FD1_PaidPage";


    // Represents the structure of the JSON data from Remote Config or local file.


    private PaidContentData _paidContentData;

    private List<string> restoreTransactionIdentifiers = new List<string>();

    private GComponent m_TopNav;
    private GButton m_BtnPaid, m_BtnRestore;

    private void FGUI_DataInit()
    {


        GRoot.inst.SetContentScaleFactor(1852, 854, UIContentScaler.ScreenMatchMode.MatchWidthOrHeight);
        UIPackage package = UIPackage.AddPackage($"FGUI/{packageName}");
        foreach (var item in package.dependencies)
        {
            UIPackage.AddPackage($"FGUI/{item["name"]}");
        }
        //UserCenterBinder.BindAll();

        UIPanel uiPanel = FindObjectOfType<UIPanel>();
        _viewMain = uiPanel.ui;

        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL("UserCenter", "PaidContentLine1"), typeof(UI_PaidContentLine1));
        //_mainView = UI_FD1_Paid.CreateInstance();

        //_mainView.fairyBatching = true;
        //_mainView.SetSize(GRoot.inst.width, GRoot.inst.height);
        //_mainView.AddRelation(GRoot.inst, RelationType.Size);
        //GRoot.inst.AddChild(_mainView);

        // 添加词汇按钮点击事件
        //viewMain = _mainView as UI_FD1_Paid;
        m_TopNav = _viewMain.GetChild("TopNav").asCom;
        GObject _backBtn = m_TopNav.GetChild("Btn_Back");
        _backBtn.position = GameUtility_FGUI.Offset_iPhoneX(_backBtn.position, 74);
        _backBtn.onClick.Add(() => { BackToHome(); });

        string savePriceFlag = GlobalVariable.SavePriceFlag;
        string defaultPrice = GlobalVariable.DefaultPrice;

        // 获取货币符号
        string currencySymbol = PlayerPrefs.GetString(GlobalVariable.SavePriceFlag, "$");

        // 获取年费价格
        string productPrice = PlayerPrefs.GetString(GlobalVariable.DefaultPrice, "2.99");

        // 计算年度原价并保留两位小数

        //string formattedPrice = float.Parse(GlobalVariable.DefaultPrice).ToString("F2");

        //Debug.Log($"formattedNowYearPrice: {formattedPrice}");

        m_BtnPaid = _viewMain.GetChild("BtnPaid").asButton;
        m_BtnRestore = _viewMain.GetChild("BtnRestore").asButton;
        m_BtnPaid.onClick.Add(OnPaidSubcribe);
        m_BtnRestore.onClick.Add(RestoreButton_Click);

        restoreTransactionIdentifiers.Clear();

    }

    private void Awake()
    {
        if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ProductId1))
        {
            GoToNextScene(backSceneName);
        }
    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    private void Start()
    {
        TextAsset appConfigTextAsset = Resources.Load<TextAsset>(configName);

        if (appConfigTextAsset == null)
        {
            Debug.LogError($"Failed to load App Config data from Resources folder.File Name:{configName}");
            //处理加载失败
            return;
        }

        string jsonContent = appConfigTextAsset.text; // Correctly access the text content
        if (string.IsNullOrEmpty(jsonContent))
        {
            Debug.LogError($"Failed to load App Config data from Resources folder.File Content is empty.{configName}");
            //处理加载失败
            return;
        }


        PaymentManager.OnStoreKitInitComplete += OnStoreKitInitComplete;
        PaymentManager.OnTransactionComplete += OnTransactionComplete;
        PaymentManager.OnRestoreComplete += OnRestoreComplete;

        canCheckUnLock = false;
        btnClickSound = Resources.Load<AudioClip>("EffectSounds/BtnClick1");


        FGUI_DataInit();
        GetRemoteConfig();

        // 初始化价格获取
        InitializePriceData();

    }



    private void GetRemoteConfig()
    {
        LoadPaidContentDataFromLocal();
        UpdateUIAccordingToPaidContentData();
    }

    /// <summary>
    /// 初始化价格数据获取
    /// </summary>
    private void InitializePriceData()
    {
        // 检查是否已经解锁
        if (!IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1))
        {
            // 添加产品ID并加载商店数据
            PaymentManager.Instance.AddProductId(GlobalVariable.IAP_ProductId1);
            PaymentManager.Instance.LoadStore();
        }
        else
        {
            Debug.Log("Content already unlocked, no need to load price data");
        }
    }

    /// 更新付费内容的 UI
    private void UpdatePaidContentUI()
    {
        Debug.Log("Executing UpdatePaidContentUI...");

        LanguageContent languageContent = GetLanguageContent();

        GComponent m_redFlag = _viewMain.GetChild("redFlag").asCom;

        m_redFlag.GetChild("titleMembershipPlan").text = languageContent.title;

        //_textAlert.text = languageContent.text_Button_Paid.message;

        Debug.Log($"Features Count: {languageContent.features.Count}");

        GList features_list = _viewMain.GetChild("features-list").asList;

        //apps_list.SetVirtual();

        int itemCount = languageContent.features.Count;

        features_list.itemRenderer = RenderListItemForAppFeatures;
        features_list.numItems = itemCount;

        // 设置按钮文本
        if (languageContent.text_Button_Paid != null && m_BtnPaid != null)
        {
            // 先尝试从ShopDataInit保存的价格中获取（App启动时已获取并保存）
            string savedFormattedPrice = PlayerPrefs.GetString(GlobalVariable.SavePriceFlag, "");

            if (!string.IsNullOrEmpty(savedFormattedPrice))
            {
                // 使用ShopDataInit已保存的格式化价格
                m_BtnPaid.text = languageContent.text_Button_Paid.message + savedFormattedPrice;

                Debug.Log($"[PriceDisplay] Using saved price from ShopDataInit: {savedFormattedPrice}");
            }
            else
            {
                // 如果没有保存的价格，使用默认价格
                string defaultFormattedPrice = GlobalVariable.DefaultPrice;
                m_BtnPaid.text = languageContent.text_Button_Paid.message + defaultFormattedPrice;

                Debug.Log($"[PriceDisplay] Using default price: {defaultFormattedPrice}");
            }

            // 如果当前页面重新获取到了最新价格，则更新显示
            if (isPriceDataLoaded && PaymentManager.Instance.Products.Count > 0)
            {
                string actualPrice = PlayerPrefs.GetString(GlobalVariable.DefaultPrice, "2.99");
                string actualCurrencySymbol = PlayerPrefs.GetString(GlobalVariable.SavePriceFlag, "$");
                string actualCurrencyCode = PaymentManager.Instance.Products[0].CurrencyCode;

                string latestFormattedPrice = FormatPrice(actualPrice, actualCurrencySymbol, actualCurrencyCode);

                // 更新为最新价格
                m_BtnPaid.text = languageContent.text_Button_Paid.message + latestFormattedPrice;

                Debug.Log($"[PriceDisplay] Updated to latest price from current session:");
                Debug.Log($"[PriceDisplay] - Price: {actualPrice}");
                Debug.Log($"[PriceDisplay] - Currency Symbol: {actualCurrencySymbol}");
                Debug.Log($"[PriceDisplay] - Currency Code: {actualCurrencyCode}");
                Debug.Log($"[PriceDisplay] - Latest Formatted Price: {latestFormattedPrice}");
            }
        }

        if (languageContent.text_Button_Restore != null && m_BtnRestore != null)
        {
            m_BtnRestore.text = languageContent.text_Button_Restore.message;
        }
    }

    void RenderListItemForAppFeatures(int index, GObject item)
    {
        Debug.Log($"obj:{index}");

        UI_PaidContentLine1 itemLine = (UI_PaidContentLine1)item;

        // 获取当前语言的内容
        LanguageContent languageContent = GetLanguageContent();

        // 检查是否有 feature 列表
        if (languageContent == null || languageContent.features == null || index >= languageContent.features.Count)
        {
            Debug.LogError($"Invalid feature index: {index}, or languageContent is null.");
            return;
        }

        // 获取对应的 feature
        Paid_Features feature = languageContent.features[index];
        Debug.Log($"feature.title:{feature.title}");
        // 更新 UI
        itemLine.m_title.text = feature.title;

    }

    private LanguageContent GetLanguageContent()
    {
        if (_paidContentData == null)
        {
            Debug.LogError("PaidContentData is null");
            return null;
        }

        string languageSuffix = GlobalVariable.GetOSLanguageSuffix();
        Debug.Log($"Current language suffix: {languageSuffix}");

        LanguageContent result;
        switch (languageSuffix)
        {
            case "-en":
                result = _paidContentData.en;
                Debug.Log("Selected English language content");
                break;
            case "-sc":
                result = _paidContentData.zh_CN;
                Debug.Log("Selected Simplified Chinese language content");
                break;
            case "-tc":
                result = _paidContentData.zh_TW;
                Debug.Log("Selected Traditional Chinese language content");
                break;
            default:
                result = _paidContentData.en;
                Debug.Log($"Unknown language suffix: {languageSuffix}, defaulting to English");
                break;
        }

        if (result == null)
        {
            Debug.LogError($"Selected language content is null for suffix: {languageSuffix}");
            // 尝试使用英文作为备选
            return _paidContentData.en;
        }

        return result;
    }



    /// 加载本地 JSON 数据并更新 UI
    private void LoadPaidContentDataFromLocal()
    {
        Debug.Log("LoadPaidContentDataFromLocal");

        TextAsset localJsonFile = Resources.Load<TextAsset>("Configs/Data_FD1_PaidPage");
        if (localJsonFile == null || string.IsNullOrEmpty(localJsonFile.text))
        {
            Debug.LogError("Error: localJsonFile is null or empty. Check if the JSON file is in the correct Resources folder.");
            return;
        }

        // 打印原始 JSON 内容（部分）
        string jsonPreview = localJsonFile.text.Length > 200
            ? localJsonFile.text.Substring(0, 200) + "..."
            : localJsonFile.text;
        Debug.Log($"Raw JSON content (preview): {jsonPreview}");

        try
        {
            _paidContentData = JsonConvert.DeserializeObject<PaidContentData>(localJsonFile.text);
            if (_paidContentData == null)
            {
                Debug.LogError("Error: Deserialized PaidContentData object is null.");
                return;
            }

            Debug.Log($"Parsed JSON successfully. Version: {_paidContentData.version}");

            // 添加调试信息
            LanguageContent currentLang = GetLanguageContent();
            if (currentLang != null)
            {
                Debug.Log($"Current language content: Title={currentLang.title}, Features count={currentLang.features?.Count ?? 0}");
                if (currentLang.features != null)
                {
                    for (int i = 0; i < currentLang.features.Count; i++)
                    {
                        Debug.Log($"Feature {i}: {currentLang.features[i]?.title}");
                    }
                }
            }
            else
            {
                Debug.LogError("Current language content is null");
            }

            // 更新 UI
            UpdatePaidContentUI();
        }
        catch (Exception ex)
        {
            Debug.LogError("JSON Parsing Error: " + ex.Message);
        }
    }

    /// 远程配置拉取后更新 UI
    private void UpdateUIAccordingToPaidContentData()
    {
        UpdatePaidContentUI();
    }

    private async Task GoToNextScene(string scnenName, int waitTime = 0)
    {
        if (isTransitioning) return;

        isTransitioning = true;
        Debug.Log($"Switching to {scnenName} scene...");

        await Task.Delay(waitTime);

        // 确保目标场景名称正确
        GameUtility.GoToSceneName_NotAddSuffix(scnenName);
    }

    void PlayClickSound()
    {
        if (btnClickSound)
        {
            SoundManager.PlaySFX(btnClickSound, false, 0, 1);
        }
        else
        {
            Debug.LogError("not found btnClickSound");
        }

    }

    void GoToSignIn()
    {
        PlayClickSound();
        GoToNextScene("UserCenterSignIn");
    }

    void OnPaidSubcribe()
    {
        PlayClickSound();
        SetPaidButtons(false);
        CheckInternet();
    }

    void RestoreButton_Click()
    {
        PlayClickSound();
        CheckInternetForRestore();
    }

    void HandleOnVerificationComplete(VerificationResponse response)
    {
        IOSNativePopUpManager.showMessage("Verification", "Transaction verification status: " + response.Status.ToString());
        UnlockProducts(GlobalVariable.IAP_ProductId1);
        Debug.Log("ORIGINAL JSON: " + response.OriginalJSON);

    }

    void SetMessageText(string key)
    {
        //_messageBox.visible = true;

        //string local_message = _config_data[key][$"text{GlobalVariable.GetOSLanguageSuffix()}"].ToString();

        //_messageGText.text = local_message;
    }

    void GetShopInfo()
    {
        action = ActionType.shop;

        PaymentManager.Instance.LoadStore();
    }




    /// <summary>
    /// 安全地隐藏进度条，避免崩溃
    /// </summary>
    private void SafeHideProgressDialog()
    {
        try
        {
            NativeDialogs.Instance.HideProgressDialog();
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to hide progress dialog: {ex.Message}");
        }
    }

    void OnDestroy()
    {
        SafeHideProgressDialog();
        PaymentManager.OnStoreKitInitComplete -= OnStoreKitInitComplete;
        PaymentManager.OnTransactionComplete -= OnTransactionComplete;
        PaymentManager.OnRestoreComplete -= OnRestoreComplete;

        // 移除返回按钮的事件监听
        GObject _backBtn = m_TopNav.GetChild("Btn_Back");
        _backBtn.onClick.Clear();

        m_BtnPaid.onClick.Clear();
        m_BtnRestore.onClick.Clear();


    }

    async Task BackToHome()
    {
        PlayClickSound();
        await GoToNextScene(backSceneName);
    }

    // 数据类已移动到 PaidContentDataClasses.cs 文件中

    public class AppleOrderReceipt
    {
        public string receipt;
        public string app_id;
    }


    private void ShowError(string message)
    {
        SafeHideProgressDialog();
        Debug.LogError($"Error: {message}");


        NativeDialogs.Instance.ShowMessageBox(
            "Error",
            message,
            new string[] { "Ok" },
            false,
            (string b) => { }
        );

        //_textAlert.color = Color.red;
        //_textAlert.text = message;

        SetPaidButtons(true);
    }

    private void ShowMessage(string message)
    {

        //_textAlert.color = GameUtility_FGUI.ConvertHex16_ToColor("#339900");
        //_textAlert.text = message;
        NativeDialogs.Instance.ShowMessageBox(
           "",
           message,
           new string[] { "Ok" },
           false,
           (string b) => { }
       );
    }

    private async void ShowSuccess(string transactionId, string productId, bool needMathLogin = false)
    {

        SafeHideProgressDialog();

        m_BtnPaid.visible = false;

        ShowMessage("Purchase Successful");

        await GoToNextScene("ChineseEasyHome", 3000);

    }

    private void OnStoreKitInitComplete(SA.Common.Models.Result result)
    {
        // 不在这里隐藏进度条，保持进度条显示直到交易完成
        // NativeDialogs.Instance.HideProgressDialog();

        if (result.IsSucceeded)
        {
            // 保存格式化的货币符号
            if (PaymentManager.Instance.Products.Count > 0)
            {
                var product = PaymentManager.Instance.Products[0];
                string formattedSymbol = GetFormattedCurrencySymbol(product.CurrencyCode);
                PlayerPrefs.SetString(GlobalVariable.SavePriceFlag, formattedSymbol);
            }

            foreach (Product product in PaymentManager.Instance.Products)
            {
                string priceInfo = product.CurrencySymbol + product.Price;
                Debug.Log($"Product ID: {product.Id}");
                Debug.Log($"Currency Symbol: {product.CurrencySymbol}");
                Debug.Log($"Currency Code: {product.CurrencyCode}");
                Debug.Log($"Price: {product.Price}");

                // 格式化价格并保存（与ShopDataInit保持一致）
                string formattedPrice = FormatPrice(product.Price.ToString(),
                    product.CurrencySymbol,
                    product.CurrencyCode);

                if (product.Price.ToString() != "-0.99")
                {
                    // 保存格式化后的价格到SavePriceFlag（与ShopDataInit一致）
                    PlayerPrefs.SetString(GlobalVariable.SavePriceFlag, formattedPrice);

                    // 保存原始价格到DefaultPrice
                    PlayerPrefs.SetString(GlobalVariable.DefaultPrice, product.Price.ToString());

                    Debug.Log($"[PriceDisplay] Saved updated price: {formattedPrice}");
                }

                // 如果有产品配置，也保存到对应的key
                if (productConfigs.TryGetValue(product.Id, out IAPProduct productConfig))
                {
                    if (product.Price.ToString() != "-0.99")
                    {
                        PlayerPrefs.SetString(productConfig.saveKey, formattedPrice);
                    }
                }
            }

            // 确保所有更改都被保存
            PlayerPrefs.Save();

            // 标记价格数据已从App Store加载
            isPriceDataLoaded = true;

            // 更新UI显示价格
            UpdatePaidContentUI();

            if (action == ActionType.buy)
            {
                AddProductId();
                //NativeDialogs.Instance.ShowProgressDialog("Processing Purchase", "", false, false);

                //ShowMessage("Processing Purchase");
            }
            else if (action == ActionType.restore)
            {
                RestoreAction();
            }
            else
            {

            }

        }
        else
        {
            // 如果初始化失败，隐藏进度条
            SafeHideProgressDialog();

            NativeDialogs.Instance.ShowMessageBox(
                "StoreKit Init Failed",
                "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Message,
                new string[] { "OK" },
                false,
                (string b) => { }
            );
        }
    }

    private async void OnTransactionComplete(PurchaseResult result)
    {
        Debug.Log("OnTransactionComplete: " + result.ProductIdentifier);
        Debug.Log("OnTransactionComplete: state: " + result.State);


        if (result.State == PurchaseState.Failed)
        {
            // 交易失败，隐藏进度条
            SafeHideProgressDialog();

            //IOSNativePopUpManager.showMessage("Transaction Failed", "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Description);
            NativeDialogs.Instance.ShowMessageBox("Transaction Failed", "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Message, new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
        else
        {
            //IOSNativePopUpManager.showMessage("Store Kit Response", "product " + result.ProductIdentifier + " state: " + result.State.ToString());
            //NativeDialogs.Instance.ShowMessageBox("Store Kit Response", "product " + result.ProductIdentifier + " state: " + result.State.ToString(), new string[]{"Ok"}, false, (string b) => {

            //});

            Debug.Log("Store Kit Response:product " + result.ProductIdentifier + " state: " + result.State.ToString());
        }

        if (action == ActionType.restore)
        {
            Debug.Log($"Restore Processing a transaction:{result.TransactionIdentifier}");
            restoreTransactionIdentifiers.Add(result.TransactionIdentifier);
            UnlockProducts(result.ProductIdentifier);
        }
        else
        {
            if (isProcessingTransaction)
            {
                Debug.Log("Already processing a transaction, skipping...");
                return;
            }

            Debug.Log($"Processing a transaction:{result.TransactionIdentifier}");
            string transactionId = result.TransactionIdentifier;

            // 保持进度条显示，不再显示消息框
            //NativeDialogs.Instance.ShowProgressDialog("Processing Purchase", "", false, false);
            //ShowMessage("Processing Purchase");

            if (string.IsNullOrEmpty(result.TransactionIdentifier))
            {
                Debug.Log("Transaction identifier is null or empty");
                isProcessingTransaction = false;

                // 隐藏进度条
                SafeHideProgressDialog();

                ShowError("Purchase verification failed, please try again later.");
            }
            else
            {
                try
                {
                    isProcessingTransaction = true;
                    Debug.Log($"Starting purchase verification for transaction: {result.TransactionIdentifier}");

                    // 先临时解锁内容，提供更好的用户体验
                    UnlockProducts(result.ProductIdentifier);

                    // 交易完成，隐藏进度条
                    SafeHideProgressDialog();

                    GoToNextScene(backSceneName);

                }
                catch (Exception ex)
                {
                    SafeHideProgressDialog();
                    Debug.LogError($"[购买] 处理交易时出错: {ex}");
                    ShowError("Purchase verification failed, please try again later.");
                    isProcessingTransaction = false;
                }
            }
        }
    }


    private void HandleInvalidPurchase(string productId)
    {
        Debug.Log($"Purchase became invalid for product: {productId}");
        // 处理订阅失效逻辑
    }

    private async void OnRestoreComplete(RestoreResult result)
    {
        // 恢复完成，隐藏进度条
        SafeHideProgressDialog();

        if (result.IsSucceeded)
        {
            Debug.Log("restoreTransactionsFinished");
            IAppsTeamIOSUntil.MobClickEvent("restore_buy");
            UnlockProducts(GlobalVariable.IAP_ProductId1);
            canCheckUnLock = true;

            // 显示恢复成功消息
            ShowMessage("Restore completed successfully");
        }
        else
        {
            Debug.LogError("Restore failed: " + result.Error.Message);
            ShowError("Restore failed: " + result.Error.Message);
        }
    }

    void UnlockProducts(string productId)
    {
        Debug.Log("purchased product: " + productId);
        IAppsTeamIOSUntil.MobClickEvent("success_buy");
        IAppsTeamIOSUntil.AddCode(GlobalVariable.IAP_ID1);
        canCheckUnLock = true;
    }

    void CheckInternet()
    {
        if (!IAppsTeamIOSUntil.ConnectedToNetwork())
        {
            print("Please connect to the internet and try again");
            string title = GameUtility.LocalizedString("NetWorkFailed");

            NativeDialogs.Instance.ShowMessageBox(title, "", new string[] { "Ok" }, false, (string b) =>
            {

            });

            SetPaidButtons(true);
        }
        else
        {
            print("connected to internet");
            CheckCanPayments();
        }
    }

    void CheckInternet_ForShop()
    {
        if (!IAppsTeamIOSUntil.ConnectedToNetwork())
        {
            print("Please connect to the internet and try again");
            string title = GameUtility.LocalizedString("NetWorkFailed");

            NativeDialogs.Instance.ShowMessageBox(title, "", new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
        else
        {
            print("connected to internet");
            CheckCanPayments_ForShop();
        }
    }

    void CheckInternetForRestore()
    {
        if (!IAppsTeamIOSUntil.ConnectedToNetwork())
        {
            print("Please connect to the internet and try again");
            string title = GameUtility.LocalizedString("NetWorkFailed");

            NativeDialogs.Instance.ShowMessageBox(title, "", new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
        else
        {
            print("connected to internet");
            CheckCanPaymentsForRestore();
        }
    }

    void CheckCanPayments()
    {
        string message = "";
        //if(StoreKitBinding.canMakePayments())
        if (PaymentManager.Instance.IsInAppPurchasesEnabled)
        {
            IAppsTeamIOSUntil.MobClickEvent("click_buy");

            actionId = 1;
            action = ActionType.buy;

            // 获取当前语言的处理中文本
            LanguageContent languageContent = GetLanguageContent();
            string processingMessage = "Processing Purchase..."; // 默认消息

            if (languageContent != null && languageContent.text_Button_Paid != null &&
                !string.IsNullOrEmpty(languageContent.text_Button_Paid.processingMessage))
            {
                processingMessage = languageContent.text_Button_Paid.processingMessage;
            }

            // 显示进度条
            try
            {
                NativeDialogs.Instance.ShowProgressDialog(processingMessage, "", false, false);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to show progress dialog: {ex.Message}");
                // 继续执行，不让进度条显示失败阻止购买流程
            }

            AddProductId();
            PaymentManager.Instance.LoadStore();

            //message = GameUtility.LocalizedString("Loading Product Data");
            //message = GameUtility.LocalizedString("in the process of purchasing");


            //PaymentManager.Instance.BuyProduct(GlobalVariable.IAP_ProductId1);

            //global::CustomProgressBar.Instance.ShowProgressBar(
            //    "in the process of purchasing",
            //    "",
            //    true // 不确定进度（循环动画）
            //);



            //ShowMessage("Processing Purchase");
        }
        else
        {
            message = GameUtility.LocalizedString("alertText1");
            string title = GameUtility.LocalizedString("alert");
            string btn1 = GameUtility.LocalizedString("close");

            NativeDialogs.Instance.ShowMessageBox(title, message, new string[] { btn1 }, false, (string b) =>
            {

            });

            SetPaidButtons(true);
        }
    }

    void CheckCanPayments_ForShop()
    {
        string message = "";
        //if(StoreKitBinding.canMakePayments())
        if (PaymentManager.Instance.IsInAppPurchasesEnabled)
        {
            GetShopInfo();
        }
        else
        {
            message = GameUtility.LocalizedString("alertText1");
            string title = GameUtility.LocalizedString("alert");
            string btn1 = GameUtility.LocalizedString("close");

            NativeDialogs.Instance.ShowMessageBox(title, message, new string[] { btn1 }, false, (string b) =>
            {
                ShowError("");
            });
        }
    }

    void CheckCanPaymentsForRestore()
    {
        string message = "";
        //if(StoreKitBinding.canMakePayments())
        if (PaymentManager.Instance.IsInAppPurchasesEnabled)
        {
            IAppsTeamIOSUntil.MobClickEvent("restore_buy");

            // 获取当前语言的处理中文本
            LanguageContent languageContent = GetLanguageContent();
            string processingMessage = "Restoring..."; // 默认消息

            if (languageContent != null && languageContent.text_Button_Restore != null &&
                !string.IsNullOrEmpty(languageContent.text_Button_Restore.processingMessage))
            {
                processingMessage = languageContent.text_Button_Restore.processingMessage;
            }

            // 显示进度条
            try
            {
                NativeDialogs.Instance.ShowProgressDialog(processingMessage, "", false, false);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to show progress dialog: {ex.Message}");
                // 继续执行，不让进度条显示失败阻止恢复流程
            }

            //global::CustomProgressBar.Instance.ShowProgressBar(
            //    "restoring...",
            //    "",
            //    true // 不确定进度（循环动画）
            //);

            actionId = 2;
            action = ActionType.restore;
            //AddProductId();

            //PaymentManager.Instance.LoadStore();

            RestoreAction();

        }
        else
        {
            message = GameUtility.LocalizedString("alertText1");
            string title = GameUtility.LocalizedString("alert");
            string btn1 = GameUtility.LocalizedString("close");

            NativeDialogs.Instance.ShowMessageBox(title, message, new string[] { btn1 }, false, (string b) =>
            {
                ShowError("");
            });
        }
    }

    void RestoreAction()
    {
        PaymentManager.Instance.RestorePurchases();
        //string message = GameUtility.LocalizedString("restoring...");
        //global::CustomProgressBar.Instance.ShowProgressBar(
        //    "restoring...",
        //    "",
        //    true // 不确定进度（循环动画）
        //);

        // 已在CheckCanPaymentsForRestore方法中显示进度条，此处不需要再显示消息
        //ShowMessage("Restoring...");
        //SetMessageText("Restoring");
    }

    void UnLockAction()
    {


    }

    void AddProductId()
    {
        PaymentManager.Instance.BuyProduct(GlobalVariable.IAP_ProductId1);
    }

    void ShowBuyInfo(string price)
    {
        Debug.Log("ShowBuyInfo");

        //global::CustomProgressBar.Instance.ShowProgressBar(
        //    "in the process of purchasing",
        //    "",
        //    true // 不确定进度（循环动画）
        //);

        //AddProductId();
        //global::CustomProgressBar.Instance.HideProgressBar();
    }

    private string GetFormattedCurrencySymbol(string currencyCode)
    {
        switch (currencyCode)
        {
            case "CNY":
            case "JPY":
                return "¥";
            case "USD":
            case "AUD":
            case "CAD":
                return "$";
            case "EUR":
                return "€";
            case "TWD":
                return "NT$";
            case "HKD":
                return "HK$";
            case "SGD":
                return "S$";
            default:
                return currencyCode;
        }
    }

    private string FormatPrice(string strPrice, string currencySymbol, string currencyCode)
    {
        if (currencyCode == "CNY") return "¥" + strPrice;
        if (currencyCode == "USD") return "$" + strPrice;
        if (currencyCode == "AUD") return "$" + strPrice;
        if (currencyCode == "EUR") return strPrice + "€";
        if (currencyCode == "TWD") return "NT$" + strPrice;
        if (currencyCode == "HKD") return "HK$" + strPrice;
        if (currencyCode == "JPY") return "¥" + strPrice;
        if (currencyCode == "CAD") return "$" + strPrice;
        if (currencyCode == "SGD") return "S$" + strPrice;
        if (currencySymbol == "$") return currencySymbol + strPrice;
        if (currencySymbol == "¥") return currencySymbol + strPrice;
        if (currencySymbol == "€") return strPrice + currencySymbol;

        return currencyCode + " " + strPrice;
    }

    void SetPaidButtons(bool canShow = true)
    {
        m_BtnPaid.visible = canShow;
        m_BtnRestore.visible = canShow;
    }

}


