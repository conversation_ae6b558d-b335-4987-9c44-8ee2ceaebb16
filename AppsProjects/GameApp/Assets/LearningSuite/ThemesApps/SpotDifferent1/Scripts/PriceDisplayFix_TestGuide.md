# 价格显示修复测试指南

## 修复内容概述

本次修复解决了 `UserPaidPageControl` 中价格显示的问题：

### 修复前的问题
1. **硬编码USD货币代码**：在 `UpdatePaidContentUI` 方法中直接传入 "USD"，导致所有地区都显示美元格式
2. **缺少App Store价格获取**：没有正确从App Store获取本地化价格信息
3. **价格更新时机不正确**：在App Store价格获取完成前就设置按钮文本

### 修复后的改进
1. **正确的价格显示流程**：
   - 首先显示ShopDataInit在App启动时保存的价格
   - 如果没有保存的价格，显示默认价格
   - 当UserPaidPageControl获取到最新价格时，更新显示
2. **统一的价格保存机制**：UserPaidPageControl和ShopDataInit都保存格式化后的价格到相同的PlayerPrefs键
3. **动态获取货币代码**：从App Store返回的产品信息中获取实际的货币代码
4. **无缝的用户体验**：用户不会看到"Loading..."，而是立即看到已保存的价格

## 测试步骤

### 1. 基本功能测试
- 启动应用（ShopDataInit会在后台获取并保存价格）
- 进入付费页面
- 观察按钮文本是否立即显示ShopDataInit保存的价格（无Loading状态）
- 如果UserPaidPageControl获取到更新的价格，观察是否会更新显示

### 2. 不同地区价格测试
在不同App Store地区测试价格显示：

#### 美国地区 (USD)
- 预期格式：`$X.XX`
- 货币代码：USD

#### 中国大陆 (CNY)
- 预期格式：`¥X.XX`
- 货币代码：CNY

#### 欧盟地区 (EUR)
- 预期格式：`X.XX€`
- 货币代码：EUR

#### 日本 (JPY)
- 预期格式：`¥XXX`
- 货币代码：JPY

#### 台湾 (TWD)
- 预期格式：`NT$XX`
- 货币代码：TWD

#### 香港 (HKD)
- 预期格式：`HK$XX`
- 货币代码：HKD

### 3. 调试日志检查
在控制台中查看以下调试信息：

**首次显示时（使用ShopDataInit保存的价格）：**
```
[PriceDisplay] Using saved price from ShopDataInit: $X.XX
```

**如果没有保存的价格：**
```
[PriceDisplay] Using default price: $0.99
```

**当获取到最新价格时：**
```
[PriceDisplay] Updated to latest price from current session:
[PriceDisplay] - Price: X.XX
[PriceDisplay] - Currency Symbol: $
[PriceDisplay] - Currency Code: USD
[PriceDisplay] - Latest Formatted Price: $X.XX
[PriceDisplay] Saved updated price: $X.XX
```

### 4. 错误处理测试
- 网络断开时的价格显示
- App Store连接失败时的处理
- 产品信息获取失败时的回退机制

## 代码变更说明

### 新增字段
```csharp
private bool isPriceDataLoaded = false; // 标志位：价格数据是否已从App Store加载
```

### 修改的方法
1. `UpdatePaidContentUI()` - 添加价格加载状态检查
2. `OnStoreKitInitComplete()` - 添加价格数据加载标志和UI更新
3. `InitializePriceData()` - 新增方法，初始化价格数据获取

### 价格格式化逻辑
`FormatPrice()` 方法根据货币代码正确格式化价格：
- CNY/JPY: ¥前缀
- USD/AUD/CAD: $前缀  
- EUR: €后缀
- TWD: NT$前缀
- HKD: HK$前缀
- SGD: S$前缀

## 预期结果

修复后，用户在不同地区看到的价格应该：
1. 显示正确的本地货币符号
2. 使用正确的价格格式
3. 反映App Store中的实际价格
4. 在价格加载过程中显示适当的加载提示

## 注意事项

1. 确保在测试时连接到相应地区的App Store
2. 价格显示可能需要几秒钟的加载时间
3. 如果App Store连接失败，会显示 "Loading..." 状态
4. 调试日志可以帮助诊断价格获取问题
