using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using MiniJSON_New;
using DG.Tweening;
using LitJson;
using System.Globalization;
using FD1_UI.FD1_LevelPage;
using System.Collections;
using FD1_UI.UserCenter;
using Newtonsoft.Json;
using System;



public class FD1_LevelPage : MonoBehaviour
{
    public string packageName = "FD1_LevelPage";
    public AudioClip btnClickSound;


    private float current_scrollPane_posX = 0;
    private GList _list;
    private JsonData app_config_data, cards_data;
    private string current_category;
    private string[] row_category_array;
    private int cards_count;
    private string card_packageName;

    private bool isGoingNextScene = false;

    private AudioSource audioSource;

    private GComponent _viewMain;

    private GList m_List_Catagory;
    private GComponent m_TopNav;
    private GButton btn_UpgradeAdFree;

    private PaidContentData _paidContentData;

    // Start is called before the first frame update
    void Start()
    {
        //PlayerPrefs.DeleteAll();
        audioSource = gameObject.AddComponent<AudioSource>();
        audioSource.spatialBlend = 0;

        FD1_GlobalVariable.current_page_no = 1;
        current_category = FD1_GlobalVariable.current_category;
        FGUI_DataInit();
        LoadPaidContentDataFromLocal();

    }

    void FGUI_DataInit()
    {
        string str_app_config_data = Resources.Load("Configs/Data_FD1_LevelPage").ToString();
        app_config_data = JsonMapper.ToObject(str_app_config_data);

         GRoot.inst.SetContentScaleFactor(1852, 854, UIContentScaler.ScreenMatchMode.MatchWidthOrHeight);

        UIPackage package = UIPackage.AddPackage($"FGUI/{packageName}");
        foreach (var item in package.dependencies)
        {
            UIPackage.AddPackage($"FGUI/{item["name"]}");
        }


        UIPanel uiPanel = FindObjectOfType<UIPanel>();
        _viewMain = uiPanel.ui;
        //FD1_LevelPageBinder.BindAll();
        //_mainView = UI_FD1_LevelPage.CreateInstance();
        //_mainView.fairyBatching = true;
        //_mainView.SetSize(GRoot.inst.width, GRoot.inst.height);
        //_mainView.AddRelation(GRoot.inst, RelationType.Size);
        //GRoot.inst.AddChild(_mainView);

        //_viewMain = _mainView as UI_FD1_LevelPage;

        //GRoot.inst.AddChild(_mainView);

        m_List_Catagory = _viewMain.GetChild("List_Catagory").asList;
        m_TopNav = _viewMain.GetChild("TopNav").asCom;


        GObject _backBtn = m_TopNav.GetChild("Btn_Back");
        _backBtn.position = GameUtility_FGUI.Offset_iPhoneX(_backBtn.position, 74);
        _backBtn.onClick.Add(() => { GoToHome(); });

        btn_UpgradeAdFree = _viewMain.GetChild("Btn_UpgradeAdFree").asButton;
        GLoader imgText = btn_UpgradeAdFree.GetChild("icon").asLoader;

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            GameUtility_FGUI.ChangeSprite(imgText, "FD1_LevelPage", "Txt-upgradeToAdFreeVersion-sc");
        }
        else if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
        {
            GameUtility_FGUI.ChangeSprite(imgText, "FD1_LevelPage", "Txt-upgradeToAdFreeVersion-tc");
        }
        else
        {
            GameUtility_FGUI.ChangeSprite(imgText, "FD1_LevelPage", "Txt-upgradeToAdFreeVersion-en");
        }

        btn_UpgradeAdFree.onClick.Add(() => { GoToPaid(); });

        if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ProductId1))
        {
            btn_UpgradeAdFree.visible = false;
        }


        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL(packageName, "Btn_LevelPage"), typeof(UI_Btn_LevelPage));


        int category_count = app_config_data["Config"]["category"].Count;

        int row_category_array_count = category_count;


        Debug.Log($"row_category_array_count : {row_category_array_count}");

        row_category_array = new string[row_category_array_count];

        // 填充row_category_array数组
        for (int i = 0; i < row_category_array_count; i++)
        {
            row_category_array[i] = app_config_data["Config"]["category"][i].ToString();
            Debug.Log($"Added category at index {i}: {row_category_array[i]}");
        }

        _list = _viewMain.GetChild("List_Catagory").asList;

        _list.itemRenderer = RenderListItem;
        _list.numItems = row_category_array_count;

    }

    void GoToHome()
    {
        AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/BtnClick1");
        SoundManager.PlaySFX(btnClip, false, 0, 1);

        StartCoroutine(GoToScene("FD1_HomePage"));
    }

    void GoToPaid()
    {
        AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/BtnClick1");
        SoundManager.PlaySFX(btnClip, false, 0, 1);

        StartCoroutine(GoToScene("FD1_Paid"));
    }


    void RenderListItem(int index, GObject obj)
    {
        UI_Btn_LevelPage item = (UI_Btn_LevelPage)obj;
        item.SetPivot(0.5f, 0.5f);

        string categoryName = row_category_array[index];
        Debug.Log($"row_category_data : {categoryName}");

        GLoader icon = item.GetChild("icon").asLoader;
        GLoader category_title = item.GetChild("image_title").asLoader;

        string title_name = categoryName;

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            title_name += "-sc";
        }
        else if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
        {
            title_name += "-tc";
        }
        else
        {
            title_name += "-en";
        }

        Debug.Log($"title_name : {title_name}");

        GameUtility_FGUI.ChangeSprite(icon, packageName, categoryName);
        GameUtility_FGUI.ChangeSprite(category_title, packageName, title_name);

        // 添加点击事件
        item.onClick.Set(() =>
        {
            // 保存当前选择的类别
            FD1_GlobalVariable.current_category = categoryName;

            // 播放点击音效
            AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/BtnClick1");
            SoundManager.PlaySFX(btnClip, false, 0, 1);

            // 跳转到关卡场景
            StartCoroutine(GoToScene("FD1_Levels"));
        });
    }

    IEnumerator GoToScene(string go_scene_name)
    {
        yield return new WaitForSeconds(0.3f);

        if (!isGoingNextScene)
        {
            isGoingNextScene = true;

            // 销毁所有事件监听器
            RemoveAllEventListeners();
            yield return new WaitForSeconds(0.5f);
            GameUtility.GoToSceneName_NotAddSuffix(go_scene_name);
        }
    }

    // 销毁所有事件监听器
    private void RemoveAllEventListeners()
    {
        // 移除返回按钮的事件监听
        GObject _backBtn = m_TopNav.GetChild("Btn_Back");
        _backBtn.onClick.Clear();
        btn_UpgradeAdFree.onClick.Clear();

        // 移除所有列表项的事件监听
        if (_list != null)
        {
            for (int i = 0; i < _list.numChildren; i++)
            {
                GObject obj = _list.GetChildAt(i);
                if (obj is UI_Btn_LevelPage)
                {
                    (obj as UI_Btn_LevelPage).onClick.Clear();
                }
            }
        }
    }


    private void LoadPaidContentDataFromLocal()
    {
        Debug.Log("LoadPaidContentDataFromLocal");

        TextAsset localJsonFile = Resources.Load<TextAsset>("Configs/Data_FD1_PaidPage");
        if (localJsonFile == null || string.IsNullOrEmpty(localJsonFile.text))
        {
            Debug.LogError("Error: localJsonFile is null or empty. Check if the JSON file is in the correct Resources folder.");
            return;
        }

        // 打印原始 JSON 内容（部分）
        string jsonPreview = localJsonFile.text.Length > 200
            ? localJsonFile.text.Substring(0, 200) + "..."
            : localJsonFile.text;
        Debug.Log($"Raw JSON content (preview): {jsonPreview}");

        try
        {
            _paidContentData = JsonConvert.DeserializeObject<PaidContentData>(localJsonFile.text);
            if (_paidContentData == null)
            {
                Debug.LogError("Error: Deserialized PaidContentData object is null.");
                return;
            }

            Debug.Log($"Parsed JSON successfully. Version: {_paidContentData.version}");

            // 添加调试信息
            LanguageContent currentLang = GetLanguageContent();
            if (currentLang != null)
            {
                Debug.Log($"Current language content: Title={currentLang.title}, Features count={currentLang.features?.Count ?? 0}");
                if (currentLang.features != null)
                {
                    for (int i = 0; i < currentLang.features.Count; i++)
                    {
                        Debug.Log($"Feature {i}: {currentLang.features[i]?.title}");
                    }
                }
            }
            else
            {
                Debug.LogError("Current language content is null");
            }

            // 更新 UI
            UpdatePaidContentUI();
        }
        catch (Exception ex)
        {
            Debug.LogError("JSON Parsing Error: " + ex.Message);
        }
    }

    /// 更新付费内容的 UI
    private void UpdatePaidContentUI()
    {
        Debug.Log("Executing UpdatePaidContentUI...");

        LanguageContent languageContent = GetLanguageContent();
        btn_UpgradeAdFree.GetChild("title").text = languageContent.title;
    }

    private LanguageContent GetLanguageContent()
    {
        if (_paidContentData == null)
        {
            Debug.LogError("PaidContentData is null");
            return null;
        }

        string languageSuffix = GlobalVariable.GetOSLanguageSuffix();
        Debug.Log($"Current language suffix: {languageSuffix}");

        LanguageContent result;
        switch (languageSuffix)
        {
            case "-en":
                result = _paidContentData.en;
                Debug.Log("Selected English language content");
                break;
            case "-sc":
                result = _paidContentData.zh_CN;
                Debug.Log("Selected Simplified Chinese language content");
                break;
            case "-tc":
                result = _paidContentData.zh_TW;
                Debug.Log("Selected Traditional Chinese language content");
                break;
            default:
                result = _paidContentData.en;
                Debug.Log($"Unknown language suffix: {languageSuffix}, defaulting to English");
                break;
        }

        if (result == null)
        {
            Debug.LogError($"Selected language content is null for suffix: {languageSuffix}");
            // 尝试使用英文作为备选
            return _paidContentData.en;
        }

        return result;
    }

}
